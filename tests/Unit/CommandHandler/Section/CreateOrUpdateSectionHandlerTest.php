<?php

declare(strict_types=1);

namespace App\Tests\Unit\CommandHandler\Section;

use App\Command\Section\CreateOrUpdateSection;
use App\Entity\SectionType;
use PHPUnit\Framework\TestCase;

class CreateOrUpdateSectionHandlerTest extends TestCase
{
    public function testCommandHandlerIntegrationWithoutDatabase(): void
    {
        // This test verifies that our Command can be created and contains the expected data
        // The actual handler would be tested in integration tests with a real database

        $command = new CreateOrUpdateSection(
            name: 'Test Section',
            sectionType: SectionType::GeneralHealth,
            published: true,
            status: true,
            medicalConditionSections: [
                ['medicalConditionId' => '123', 'name' => 'Test Condition']
            ],
            productSections: [
                ['productId' => '456', 'name' => 'Test Product']
            ],
            questionSections: [
                ['question' => 1, 'sort' => 0]
            ]
        );

        // Verify the command contains the expected data that would be processed by the handler
        $this->assertEquals('Test Section', $command->getName());
        $this->assertEquals(SectionType::GeneralHealth, $command->getSectionType());
        $this->assertTrue($command->isPublished());
        $this->assertTrue($command->getStatus());

        // Verify product sections data
        $medicalConditions = $command->getMedicalConditionSections();
        $this->assertCount(1, $medicalConditions);
        $this->assertEquals('123', $medicalConditions[0]['medicalConditionId']);
        $this->assertEquals('Test Condition', $medicalConditions[0]['name']);

        $productSections = $command->getProductSections();
        $this->assertCount(1, $productSections);
        $this->assertEquals('456', $productSections[0]['productId']);
        $this->assertEquals('Test Product', $productSections[0]['name']);

        // Verify question sections data
        $questionSections = $command->getQuestionSections();
        $this->assertCount(1, $questionSections);
        $this->assertEquals(1, $questionSections[0]['question']);
        $this->assertEquals(0, $questionSections[0]['sort']);
    }

    public function testCommandWithMinimalData(): void
    {
        $command = new CreateOrUpdateSection(
            name: 'Minimal Section',
            sectionType: SectionType::Other,
            published: false
        );

        $this->assertEquals('Minimal Section', $command->getName());
        $this->assertEquals(SectionType::Other, $command->getSectionType());
        $this->assertFalse($command->isPublished());
        $this->assertNull($command->getStatus());
        $this->assertEmpty($command->getMedicalConditionSections());
        $this->assertEmpty($command->getProductSections());
        $this->assertEmpty($command->getQuestionSections());
        $this->assertNull($command->isDeleted());
    }
}
