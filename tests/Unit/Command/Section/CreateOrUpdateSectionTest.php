<?php

declare(strict_types=1);

namespace App\Tests\Unit\Command\Section;

use App\Command\Section\CreateOrUpdateSection;
use App\Entity\SectionType;
use PHPUnit\Framework\TestCase;

class CreateOrUpdateSectionTest extends TestCase
{
    public function testCreateOrUpdateSectionCommand(): void
    {
        $command = new CreateOrUpdateSection(
            name: 'Test Section',
            sectionType: SectionType::GeneralHealth,
            published: true,
            status: true,
            medicalConditionSections: [
                ['medicalConditionId' => '123', 'name' => 'Test Condition']
            ],
            productSections: [
                ['productId' => '456', 'name' => 'Test Product']
            ],
            questionSections: [
                ['id' => 1, 'sort' => 0, 'question' => 1, 'section' => 1]
            ]
        );

        $this->assertEquals('Test Section', $command->getName());
        $this->assertEquals(SectionType::GeneralHealth, $command->getSectionType());
        $this->assertTrue($command->isPublished());
        $this->assertTrue($command->getStatus());
        $this->assertCount(1, $command->getMedicalConditionSections());
        $this->assertCount(1, $command->getProductSections());
        $this->assertCount(1, $command->getQuestionSections());
    }

    public function testCreateOrUpdateSectionCommandWithMinimalData(): void
    {
        $command = new CreateOrUpdateSection(
            name: 'Minimal Section',
            sectionType: SectionType::Other,
            published: false
        );

        $this->assertEquals('Minimal Section', $command->getName());
        $this->assertEquals(SectionType::Other, $command->getSectionType());
        $this->assertFalse($command->isPublished());
        $this->assertNull($command->getStatus());
        $this->assertEmpty($command->getMedicalConditionSections());
        $this->assertEmpty($command->getProductSections());
        $this->assertEmpty($command->getQuestionSections());
    }
}
