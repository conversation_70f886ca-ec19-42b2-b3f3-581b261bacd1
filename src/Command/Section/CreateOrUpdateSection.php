<?php

declare(strict_types=1);

namespace App\Command\Section;

use App\Entity\SectionType;
use Symfony\Component\Validator\Constraints as Assert;

final readonly class CreateOrUpdateSection
{
    /**
     * @param string $name
     * @param SectionType $sectionType
     * @param bool $published
     * @param bool|null $status
     * @param array $medicalConditionSections
     * @param array $productSections
     * @param array $questionSections
     * @param mixed|null $generalSections
     * @param int|null $deleted
     * @param int|null $id
     * @param string|null $createdAt
     * @param string|null $updatedAt
     */
    public function __construct(
        #[Assert\NotBlank]
        #[Assert\Length(max: 200)]
        private readonly string $name,

        #[Assert\NotNull]
        private readonly SectionType $sectionType,

        #[Assert\NotNull]
        private readonly bool $published,

        /**
         * @deprecated This property is deprecated. Please use the `published` property instead.
         */
        private ?bool       $status = null,

        private array       $medicalConditionSections = [],

        private array       $productSections = [],

        private array       $questionSections = [],

        private mixed       $generalSections = null,

        private ?int        $deleted = null,

        private ?int        $id = null,

        private ?string     $createdAt = null,

        private ?string     $updatedAt = null,
    ) {
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return SectionType
     */
    public function getSectionType(): SectionType
    {
        return $this->sectionType;
    }

    /**
     * @return bool
     */
    public function isPublished(): bool
    {
        return $this->published;
    }

    /**
     * @return bool|null
     */
    public function getStatus(): ?bool
    {
        return $this->status;
    }

    /**
     * @return array
     */
    public function getMedicalConditionSections(): array
    {
        return $this->medicalConditionSections;
    }

    /**
     * @return array
     */
    public function getProductSections(): array
    {
        return $this->productSections;
    }

    /**
     * @return array
     */
    public function getQuestionSections(): array
    {
        return $this->questionSections;
    }

    /**
     * @return mixed
     */
    public function getGeneralSections(): mixed
    {
        return $this->generalSections;
    }

    /**
     * @return int|null
     */
    public function isDeleted(): ?int
    {
        return $this->deleted;
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @return string|null
     */
    public function getCreatedAt(): ?string
    {
        return $this->createdAt;
    }

    /**
     * @return string|null
     */
    public function getUpdatedAt(): ?string
    {
        return $this->updatedAt;
    }
}
