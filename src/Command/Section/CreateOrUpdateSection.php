<?php

declare(strict_types=1);

namespace App\Command\Section;

use App\Entity\SectionType;
use <PERSON>ymfony\Component\Validator\Constraints as Assert;

final class CreateOrUpdateSection
{
    /**
     * @param array<array-key, array{medicalConditionId?: string, name: string}> $medicalConditionSections
     * @param array<array-key, array{productId?: string, name: string}> $productSections
     * @param array<array-key, array{id?: int, sort: int, question: int, section?: int}> $questionSections
     */
    public function __construct(
        #[Assert\NotBlank]
        #[Assert\Length(max: 200)]
        private readonly string $name,

        #[Assert\NotNull]
        private readonly SectionType $sectionType,

        #[Assert\NotNull]
        private readonly bool $published,

        /**
         * @deprecated This property is deprecated. Please use the `published` property instead.
         */
        private readonly ?bool $status = null,

        private readonly array $medicalConditionSections = [],

        private readonly array $productSections = [],

        private readonly array $questionSections = [],

        private readonly ?int $generalSections = null,

        private readonly ?bool $deleted = null,
    ) {
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getSectionType(): SectionType
    {
        return $this->sectionType;
    }

    public function isPublished(): bool
    {
        return $this->published;
    }

    /**
     * @deprecated This property is deprecated. Please use the `published` property instead.
     */
    public function getStatus(): ?bool
    {
        return $this->status;
    }

    /**
     * @return array<array-key, array{medicalConditionId?: string, name: string}>
     */
    public function getMedicalConditionSections(): array
    {
        return $this->medicalConditionSections;
    }

    /**
     * @return array<array-key, array{productId?: string, name: string}>
     */
    public function getProductSections(): array
    {
        return $this->productSections;
    }

    /**
     * @return array<array-key, array{id?: int, sort: int, question: int, section?: int}>
     */
    public function getQuestionSections(): array
    {
        return $this->questionSections;
    }

    public function getGeneralSections(): ?int
    {
        return $this->generalSections;
    }

    public function isDeleted(): ?bool
    {
        return $this->deleted;
    }
}
