<?php

declare(strict_types=1);

namespace App\CommandHandler\Section;

use App\Command\Section\CreateOrUpdateSection;
use App\Entity\Product;
use App\Entity\ProductType;
use App\Entity\QuestionSection;
use App\Entity\Section;
use App\Repository\QuestionsRepository;
use App\Repository\SectionRepository;
use App\Service\ProductService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class CreateOrUpdateSectionHandler
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private SectionRepository $sectionRepository,
        private QuestionsRepository $questionsRepository,
        private ProductService $productService,
    ) {
    }

    public function __invoke(CreateOrUpdateSection $command): Section
    {
        $section = new Section();
        $section->setName($command->getName());
        $section->setSectionType($command->getSectionType());
        $section->setPublished($command->isPublished());
        
        // Handle deprecated status field
        if ($command->getStatus() !== null) {
            $section->setStatus($command->getStatus());
        }

        // Handle products
        $this->addProductsToSection($section, $command);

        // Handle question sections
        $this->addQuestionSectionsToSection($section, $command);

        $this->sectionRepository->add($section, true);

        return $section;
    }

    private function addProductsToSection(Section $section, CreateOrUpdateSection $command): void
    {
        $productsData = array_merge(
            $command->getMedicalConditionSections(),
            $command->getProductSections()
        );

        foreach ($productsData as $productData) {
            $productCode = $productData['medicalConditionId'] ?? $productData['productId'] ?? null;
            if (!is_string($productCode)) {
                continue;
            }

            $productType = array_key_exists('medicalConditionId', $productData)
                ? ProductType::Consult
                : ProductType::Medication;

            $productDataForService = [
                'code' => $productCode,
                'name' => $productData['name'],
                'type' => $productType,
            ];

            $product = $this->productService->createOrUpdateProduct($productDataForService, 'en');
            $section->addProduct($product);
        }
    }

    private function addQuestionSectionsToSection(Section $section, CreateOrUpdateSection $command): void
    {
        foreach ($command->getQuestionSections() as $questionSectionData) {
            $questionId = $questionSectionData['question'];
            $sort = $questionSectionData['sort'];

            $question = $this->questionsRepository->find($questionId);
            if (!$question) {
                continue; // Skip if question doesn't exist
            }

            $questionSection = new QuestionSection();
            $questionSection->setQuestion($question);
            $questionSection->setSort($sort);
            $questionSection->setSection($section);

            $section->addQuestionSection($questionSection);
        }
    }
}
