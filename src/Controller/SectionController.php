<?php

declare(strict_types=1);

namespace App\Controller;

use App\Entity\Section;
use App\Repository\SectionRepository;

use App\Service\SectionsGetServices;
use App\Service\SectionUpdateServices;
use Doctrine\ORM\NonUniqueResultException;
use Nijens\OpenapiBundle\Routing\RouteContext;
use Nijens\OpenapiBundle\Serialization\SerializationContextBuilderInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class SectionController extends AbstractController
{
    public function __construct(
        private readonly ValidatorInterface $validator,
        private readonly SerializationContextBuilderInterface $serializationContextBuilder,
        private readonly SerializerInterface $serializer,
        private readonly SectionRepository $sectionRepository,
        private readonly SectionsGetServices $sectionsGetServices,
        private readonly SectionUpdateServices $sectionUpdateServices,
    ) {
    }

    public function getAll(Request $request, string $responseSerializationSchemaObject): JsonResponse
    {
        [$total, $page, $perPage, $sections] = $this->sectionsGetServices->getSections($request);

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(
                ['total' => $total, 'page' => $page, 'perPage' => $perPage, 'sections' => $sections],
                'json',
                $this->serializationContextBuilder->getContextForSchemaObject(
                    $responseSerializationSchemaObject,
                    $request->attributes->get(RouteContext::REQUEST_ATTRIBUTE)[RouteContext::RESOURCE],
                )
            ),
            Response::HTTP_OK,
        );
    }

    /**
     * @throws NonUniqueResultException
     */
    public function get(Request $request, string $responseSerializationSchemaObject): JsonResponse
    {
        $id = intval($request->get('id'));
        $section = $this->sectionRepository->getSection($id);

        if (!$section) {
            throw new NotFoundHttpException('Section not found');
        }

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(
                $section,
                'json',
                $this->serializationContextBuilder->getContextForSchemaObject(
                    $responseSerializationSchemaObject,
                    $request->attributes->get(RouteContext::REQUEST_ATTRIBUTE)[RouteContext::RESOURCE],
                )
            ),
            Response::HTTP_OK,
        );
    }



    /**
     * @throws NonUniqueResultException
     */
    public function update(
        Request $request,
        Section $section,
        string $responseSerializationSchemaObject,
    ): JsonResponse {
        $dbSection = $this->sectionUpdateServices->updateSection($request, $section);

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(
                $dbSection,
                'json',
                $this->serializationContextBuilder->getContextForSchemaObject(
                    $responseSerializationSchemaObject,
                    $request->attributes->get(RouteContext::REQUEST_ATTRIBUTE)[RouteContext::RESOURCE],
                )
            ),
            Response::HTTP_CREATED,
        );
    }

    /**
     * @throws NonUniqueResultException
     */
    public function delete(Request $request, string $responseSerializationSchemaObject): JsonResponse
    {
        $id = intval($request->get('id'));
        $section = $this->sectionRepository->getSection($id);

        if (!$section) {
            throw new NotFoundHttpException('Section not found');
        }

        $section->setDeleted(true);
        $this->sectionRepository->updateSection($section);

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(
                $section,
                'json',
                $this->serializationContextBuilder->getContextForSchemaObject(
                    $responseSerializationSchemaObject,
                    $request->attributes->get(RouteContext::REQUEST_ATTRIBUTE)[RouteContext::RESOURCE],
                )
            ),
            Response::HTTP_CREATED,
        );
    }
}
